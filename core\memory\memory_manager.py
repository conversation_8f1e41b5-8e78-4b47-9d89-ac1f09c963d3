from datetime import datetime
import asyncio
import json
import logging
import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
import redis
import os
import aiofiles  # Add this import at the top
import openai
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from datetime import datetime
from collections import defaultdict

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger
from core.memory.redis_context import RedisClient
from core.memory.persistent_mongo import save_dialog_to_mongo, save_user_to_mongo, save_call_session_to_mongo, save_pipeline_to_mongo, save_intent_history_to_mongo
from utils.llm_call_tracker import track_llm_call


# --- MemoryLayer Interface ---
class MemoryLayer(ABC):
    @abstractmethod
    async def get(self, key: str) -> Any:
        pass

    @abstractmethod
    async def set(self, key: str, value: Any):
        pass

# --- EphemeralMemory ---
class EphemeralMemory(MemoryLayer):
    """
    Stores transient data for a single pipeline step. Cleared after each pipeline.
    Thread-safe and async-compatible.
    """
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._lock = asyncio.Lock()  # Replace threading.Lock with asyncio.Lock
        self.logger = get_module_logger("EphemeralMemory")

    async def get(self, key: str) -> Any:
        async with self._lock:  # Use async context manager
            value = self._data.get(key)
            self.logger.debug(
                "GET ephemeral memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value},
                layer="memory_ephemeral"
            )
            return value

    async def set(self, key: str, value: Any):
        async with self._lock:  # Use async context manager
            self._data[key] = value
            self.logger.debug(
                "SET ephemeral memory",
                action="set",
                input_data={"key": key, "value": value},
                layer="memory_ephemeral"
            )

    async def clear(self):
        async with self._lock:  # Use async context manager
            self.logger.debug(
                "CLEAR ephemeral memory",
                action="clear",
                layer="memory_ephemeral"
            )
            self._data.clear()

    async def get_all(self) -> Dict[str, Any]:
        async with self._lock:  # Use async context manager
            return dict(self._data)

# --- ContextualMemory ---
class ContextualMemory(MemoryLayer):
    """
    Stores session-specific data (chat history, intents, slots, etc.) in Redis.
    Thread-safe and persistent.
    """
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.redis_client = RedisClient()
        self.logger = get_module_logger(f"ContextualMemory", session_id=session_id)

    async def get(self, key: str) -> Any:
        context = await self.redis_client.get(self.session_id) or {}
        return context.get(key)

    async def set(self, key: str, value: Any):
        context = await self.redis_client.get(self.session_id) or {}
        context[key] = value
        await self.redis_client.set(self.session_id, context)

    async def clear(self):
        await self.redis_client.set(self.session_id, {})

    async def get_all(self) -> Dict[str, Any]:
        return await self.redis_client.get(self.session_id) or {}

# --- PersistentMemory ---
class PersistentMemory(MemoryLayer):
    """
    Stores long-term data in Redis. Keys are prefixed by user_id or session_id.
    Serializes complex objects to JSON.
    """
    def __init__(self):
        self.redis_client = RedisClient()  # Use the async RedisClient instead of direct redis
        self.logger = get_module_logger("PersistentMemory")

    async def _make_key(self, prefix: str, key: str) -> str:
        return f"{prefix}:{key}"

    async def get(self, key: str) -> Any:
        try:
            value = await self.redis_client.get(key)  # Use async Redis client
            if value is None:
                self.logger.debug(
                    "GET persistent memory - not found",
                    action="get",
                    input_data={"key": key},
                    output_data={"value": None},
                    layer="memory_persistent"
                )
                return None
            
            self.logger.debug(
                "GET persistent memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value},
                layer="memory_persistent"
            )
            return value
        except Exception as e:
            self.logger.error(
                "Error getting persistent memory",
                action="get",
                input_data={"key": key},
                reason=str(e),
                layer="memory_persistent"
            )
            return None

    async def set(self, key: str, value: Any):
        """
        Set a value in persistent memory.
        - If value is a dict and existing value is a dict, merge (update) them.
        - If value is a list and existing value is a list, append new items.
        - Otherwise, overwrite as before.
        """
        try:
            existing = await self.get(key)  # Use await for async method
            # Merge dicts
            if isinstance(value, dict) and isinstance(existing, dict):
                merged = existing.copy()
                merged.update(value)
                value = merged
            # Append to lists
            elif isinstance(value, list) and isinstance(existing, list):
                value = existing + value
                
            await self.redis_client.set(key, value)  # Use async Redis client
            self.logger.debug(
                "SET persistent memory",
                action="set",
                input_data={"key": key, "value": value},
                layer="memory_persistent"
            )
        except Exception as e:
            self.logger.error(
                "Error setting persistent memory",
                action="set",
                input_data={"key": key},
                reason=str(e),
                layer="memory_persistent"
            )

    async def get_all(self, prefix: str) -> Dict[str, Any]:
        try:
            pattern = f"{prefix}:*"
            keys = await self.redis_client.client.keys(pattern)
            result = {}
            for key in keys:
                result[key] = await self.get(key)  # Use await for async method
            self.logger.debug(
                "GET_ALL persistent memory",
                action="get_all",
                input_data={"prefix": prefix},
                output_data={"result": result, "count": len(result)},
                layer="memory_persistent"
            )
            return result
        except Exception as e:
            self.logger.error(
                "Error getting all persistent memory",
                action="get_all",
                input_data={"prefix": prefix},
                reason=str(e),
                layer="memory_persistent"
            )
            return {}

# --- MemoryManager ---
class MemoryManager:
    """
    Orchestrates Ephemeral, Contextual, and Persistent memory layers.
    Provides unified API for get/set/clear and explicit memory saving.
    """
    def __init__(self, session_id: str, user_id: Optional[str] = None):
        self.session_id = session_id
        self.user_id = user_id or f"anon_{session_id}"
        self.ephemeral = EphemeralMemory()
        self.contextual = ContextualMemory(session_id)
        self.persistent = PersistentMemory()
        self.logger = get_module_logger("MemoryManager", session_id=session_id)

    async def get(self, key: str) -> Any:
        # Try ephemeral memory first
        value = await self.ephemeral.get(key)
        if value is not None:
            self.logger.debug(
                "GET from ephemeral memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "ephemeral"},
                layer="memory_manager"
            )
            return value
        
        # Try contextual memory next
        value = await self.contextual.get(key)
        if value is not None:
            self.logger.debug(
                "GET from contextual memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "contextual"},
                layer="memory_manager"
            )
            return value
        
        # Finally try persistent memory
        persistent_key = f"{self.user_id}:{key}"
        value = await self.persistent.get(persistent_key)
        if value is not None:
            self.logger.debug(
                "GET from persistent memory",
                action="get",
                input_data={"key": key},
                output_data={"value": value, "source": "persistent"},
                layer="memory_manager"
            )
        else:
            self.logger.debug(
                "GET not found in any memory layer",
                action="get",
                input_data={"key": key},
                output_data={"value": None, "source": "none"},
                layer="memory_manager"
            )
        return value

    async def set(self, layer: str, key: str, value: Any):
        if layer == "ephemeral":
            await self.ephemeral.set(key, value)
        elif layer == "contextual":
            await self.contextual.set(key, value)
        elif layer == "persistent":
            persistent_key = f"{self.user_id}:{key}"
            await self.persistent.set(persistent_key, value)
        else:
            self.logger.error(
                "Unknown memory layer",
                action="set",
                input_data={"layer": layer, "key": key},
                reason=f"Unknown memory layer: {layer}",
                layer="memory_manager"
            )
            raise ValueError(f"Unknown memory layer: {layer}")
        self.logger.info(
            "SET memory value",
            action="set",
            input_data={"layer": layer, "key": key, "value": value},
            layer="memory_manager"
        )

    async def clear(self, layer: str, key: str):
        """Clear a specific key from the specified memory layer."""
        if layer == "ephemeral":
            # For ephemeral memory, we need to remove the key from the dict
            async with self.ephemeral._lock:
                if key in self.ephemeral._data:
                    del self.ephemeral._data[key]
        elif layer == "contextual":
            # For contextual memory, we need to remove the key from Redis
            context = await self.contextual.redis_client.get(self.session_id) or {}
            if key in context:
                del context[key]
                await self.contextual.redis_client.set(self.session_id, context)
        elif layer == "persistent":
            # For persistent memory, delete the key from Redis
            persistent_key = f"{self.user_id}:{key}"
            await self.persistent.redis_client.delete(persistent_key)
        else:
            self.logger.error(
                "Unknown memory layer",
                action="clear",
                input_data={"layer": layer, "key": key},
                reason=f"Unknown memory layer: {layer}",
                layer="memory_manager"
            )
            raise ValueError(f"Unknown memory layer: {layer}")

        self.logger.debug(
            "CLEAR memory key",
            action="clear",
            input_data={"layer": layer, "key": key},
            layer="memory_manager"
        )

    async def clear_ephemeral(self):
        self.logger.info(
            "Clearing ephemeral memory",
            action="clear_ephemeral",
            layer="memory_manager"
        )
        await self.ephemeral.clear()  # Use await for async method

    async def clear_contextual(self):
        self.logger.info(
            "Clearing contextual memory",
            action="clear_contextual",
            layer="memory_manager"
        )
        await self.contextual.clear()

    async def get_all_contextual(self) -> Dict[str, Any]:
        return await self.contextual.get_all()

    async def get_all_persistent(self) -> Dict[str, Any]:
        return await self.persistent.get_all(self.user_id)
    
    # get conversation history between ai and user
    async def get_conversation(self) -> list:
        context = await self.contextual.get_all()
        conversation = context.get("conversation", [])
        return conversation

    async def save_conversation_turn(self, user_message: str, ai_message: str, intent: Optional[str] = None, latency: Optional[dict] = None):
        """
        Async version: Save a conversation turn (user and AI message) to contextual memory.
        Args:
            user_message: The user's message
            ai_message: The AI's response
            intent: Optional detected intent
            latency: Optional dict of latency values for this turn (e.g., {'latencySTT': ..., ...})
        """
        conversation = await self.contextual.get("conversation") or []
        print("[DEBUG] About to save conversation:", conversation)
        print("[DEBUG] Type of conversation:", type(conversation))
        # Fetch the latest shared context and extract latency values
        context = await self.contextual.get_all()
        latency = {k: v for k, v in context.items() if k.startswith("latency") and isinstance(v, (int, float))}
        # Ensure latencyTTS is included if present
        if "latencyTTS" in context and isinstance(context["latencyTTS"], (int, float)):
            latency["latencyTTS"] = context["latencyTTS"]
        total_latency = sum(latency.values()) if latency else None
        # Add user turn
        user_turn = {
            "role": "user",
            "text": user_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        if latency:
            user_turn.update(latency)
            user_turn["total_latency"] = total_latency
        conversation.append(user_turn)
        # Add AI turn
        ai_turn = {
            "role": "ai",
            "text": ai_message,
            "timestamp": datetime.now().isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id
        }
        if latency:
            ai_turn.update(latency)
            ai_turn["total_latency"] = total_latency
        conversation.append(ai_turn)
        print("[DEBUG] Conversation after append:", conversation)
        await self.contextual.set("conversation", conversation)
        self.logger.info(
            "Saved conversation turn",
            action="save_conversation_turn",
            input_data={"user_message": user_message, "ai_message": ai_message, "intent": intent, "latency": latency},
            layer="memory_manager"
        )

    async def explicit_save(self, intent: str, slots: Dict[str, Any]):
        """
        Detects 'Remember this...' or explicit save intents and stores in persistent memory.
        Example: intent='save_preference', slots={'preference': 'language', 'value': 'en'}
        """
        if intent == "save_preference" and "preference" in slots and "value" in slots:
            key = slots["preference"]
            value = slots["value"]
            persistent_key = f"{self.user_id}:{key}"
            await self.persistent.set(persistent_key, value)  # Use await for async method
            self.logger.info(
                "Explicitly saved preference to persistent memory",
                action="explicit_save",
                input_data={"intent": intent, "slots": slots},
                output_data={"persistent_key": persistent_key, "value": value},
                layer="memory_manager"
            )
            return True
        self.logger.warning(
            "Explicit save intent not recognized or missing slots",
            action="explicit_save",
            input_data={"intent": intent, "slots": slots},
            reason="Intent not recognized or missing required slots",
            layer="memory_manager"
        )
        return False

    # TODO: Add support for advanced memory analytics and summarization in future versions.
    # def save_session_summary(self, llm_extract_func):
    #     """
    #     Extracts relevant information from contextual memory, sends it to an LLM for summarization/extraction,
    #     and saves the returned structured data to persistent memory. Call this before clearing contextual memory.
    #     Args:
    #         llm_extract_func: a function that takes session data (dict) and returns a dict of persistent info
    #     Returns:
    #         The dict of persistent info saved, for logging/debugging.
    #     """
    #     # 1. Extract contextual/session data
    #     session_data = self.get_all_contextual()
    #     self.logger.info(f"Extracting persistent info from session data for user {self.user_id}")

    #     # 2. Send to LLM for extraction 
    #     persistent_info = llm_extract_func(session_data)
    #     if not isinstance(persistent_info, dict):
    #         self.logger.error("LLM extraction did not return a dict. Nothing saved.")
    #         return None

    #     # 3. Save to persistent memory
    #     for key, value in persistent_info.items():
    #         self.set("persistent", key, value)
    #         self.logger.info(f"Saved to persistent memory: {key} = {value}")

    #     return persistent_info
    
    # def llm_extract_func(session_data):
    #     # Call your LLM here and return a dict of info to persist
    #     # For now, just a placeholder:
    #     return {"language": "en", "favorite_color": "blue"}

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def save_persistent_memory_data(self):
        """
        Save all persistent memory data to MongoDB including:
        1. Dialog log with LLM analysis
        2. User profile with session preferences
        3. Call session metadata
        4. Intent history from the session

        This method should be called before clearing contextual memory.
        """
        try:
            print("mohamed hassan something to see in memory manager ")

            # Save dialog log first (includes LLM analysis) and get metrics + score
            dialog_result = await self._save_dialog_log_internal()

            # Save user profile with session data
            # await self.save_user_profile()

            # Save call session metadata using metrics from dialog log
            await self._save_call_session_internal(dialog_result)

            # Save pipeline metadata
            await self._save_pipeline_internal()

            # Save intent history from session
            await self._save_intent_history_internal()

            self.logger.info(
                "Successfully saved all persistent memory data",
                action="save_persistent_memory_data",
                input_data={"session_id": self.session_id, "user_id": self.user_id},
                status="success",
                layer="memory_manager"
            )

        except Exception as e:
            self.logger.error(
                f"Failed to save persistent memory data: {e}",
                action="save_persistent_memory_data",
                input_data={"session_id": self.session_id, "user_id": self.user_id},
                reason=str(e),
                layer="memory_manager"
            )



    async def _save_dialog_log_internal(self):
        """
        Internal method to save the session dialog to logs/Dialog/{session_id}.txt with:
        1. LLM-generated summary
        2. Duration (from contextual/shared memory latency)
        3. LLM-generated learnings (bullet points)
        4. LLM-generated score (1-10)
        5. Conversation history in specified format

        Returns:
            dict: Contains calculated metrics and LLM score:
                - call_score: LLM-generated score (1-10)
                - total_session_latency: Total latency in ms
                - latency_avg_ms: Average latency per component
                - tts_characters_used: Total TTS characters
                - duration_sec: Session duration in seconds
        """
        try:
            session_id = self.session_id
            dialog_dir = Path("logs/Dialog")
            dialog_dir.mkdir(parents=True, exist_ok=True)
            file_path = dialog_dir / f"{session_id}.txt"

            # Gather conversation and context
            context = await self.contextual.get_all()
            conversation = context.get("conversation", [])
            # Try to get latency/duration from context
            duration_ms = 0
            latency_details = {}
            for k in context:
                if k.startswith("latency") and isinstance(context[k], (int, float)):
                    duration_ms += context[k]
                    latency_details[k] = context[k]
            print(f"[Dialog Log] Latency details for session {session_id}: {latency_details}")

            # Format conversation for LLM prompt and for file
            formatted_conversation = []
            # For latency aggregation (AI turns only) and TTS character counting
            latency_sums = {}
            total_session_latency = 0
            tts_characters_used = 0

            # Simple token tracking - just aggregate the data from contextual memory
            llm_calls = context.get("llm_calls", [])

            # Simple aggregation
            total_tokens_in = sum(call.get("input_tokens", 0) for call in llm_calls)
            total_tokens_out = sum(call.get("output_tokens", 0) for call in llm_calls)
            total_estimated_cost_usd = sum(call.get("cost_usd", 0.0) for call in llm_calls)
            token_details = llm_calls  # Just pass through the call data

            # Aggregate cost by type
            estimated_cost_usd_for_llm = sum(call.get("cost_usd", 0.0) for call in llm_calls if call.get("context") == "llm")
            estimated_cost_usd_for_tts = sum(call.get("cost_usd", 0.0) for call in llm_calls if call.get("context") == "tts")
            estimated_cost_usd_for_stt = sum(call.get("cost_usd", 0.0) for call in llm_calls if call.get("context") == "stt")

            # Process AI turns for existing metrics (latency, TTS) and formatted conversation
            for i in range(1, len(conversation), 2):  # Only AI turns (odd indices)
                ai = conversation[i] if conversation[i].get("role") == "ai" else None
                if ai:
                    for k, v in ai.items():
                        if k.startswith("latency") and isinstance(v, (int, float)):
                            latency_sums[k] = latency_sums.get(k, 0) + v
                    if "total_latency" in ai and isinstance(ai["total_latency"], (int, float)):
                        total_session_latency += ai["total_latency"]
                    # Count TTS characters from AI responses
                    tts_characters_used += len(ai.get("text", ""))

                    # Find token data for this turn
                    turn_token_data = next((detail for detail in token_details if detail.get("turn") == i), {})

                    formatted_conversation.append({
                        "timestamp": ai.get("timestamp"),
                        "agent_response": ai.get("text"),
                        "emotion": context.get("emotion", "unknown"),
                        "intent": context.get("intent", "unknown"),
                        "latency": {k: ai.get(k) for k in ai if k.startswith("latency")},
                        "total_latency": ai.get("total_latency"),
                        "tokens_in": turn_token_data.get("input_tokens", 0),
                        "tokens_out": turn_token_data.get("output_tokens", 0),
                        "cost_usd": turn_token_data.get("cost_usd", 0.0)
                    })

            # Calculate average latency
            latency_avg_ms = sum(latency_sums.values()) / len(latency_sums) if latency_sums else None

            print(f"[Dialog Log] Aggregated latency sums for session {session_id}: {latency_sums}")
            print(f"[Dialog Log] Total session latency: {total_session_latency}")
            print(f"[Dialog Log] TTS characters used: {tts_characters_used}")
            print(f"[Dialog Log] Average latency: {latency_avg_ms}")
            print(f"[Dialog Log] Total tokens in: {total_tokens_in}")
            print(f"[Dialog Log] Total tokens out: {total_tokens_out}")
            print(f"[Dialog Log] Total estimated cost: ${total_estimated_cost_usd:.6f}")
            print(f"[Dialog Log] Token details: {token_details}")

            # Prepare LLM prompt
            openai_api_key = os.getenv("OPENAI_API_KEY")
            if not openai_api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
            prompt = (
                "You are an expert evaluator and memory extractor for AI-human conversations. Given the following conversation, "
                "generate: 1) a summary of the conversation  in 2–4 sentences, highlighting the purpose of the call and key actions or issues discussed., 2) 3-5 bullet-point learnings Extract any relevant insights or personal information revealed by the user that can be stored as memory to personalize future interactions. These should be factual, context-relevant, and privacy-respectful. , "
                "3) Rate the conversation quality and the AI agent's performance on a scale of 1 to 10 (1 = very poor, 10 = excellent)., "
                "and 4) Briefly explain why the score was given, based on agent performance (e.g., clarity, helpfulness, understanding).\n"
                f"Conversation: {formatted_conversation}\n"
                "Respond in this format:\n"
                "Summary: ...\n"
                "Learnings: - {fact or preference about the user}- {goal, habit, or behavior}- {relevant context or constraint}  \n- ...\n- ...\n- ...\n"
                "Score: <number>\n"
                "Justification: ...\n"
                "\nRespond ONLY with a valid JSON object in this format (no extra text, markdown, or comments): {\"summary\": \"...\", \"learnings\": [\"...\", \"...\", \"...\"], \"score\": <number>, \"justification\": \"...\"}"
            )
            try:
                response = await openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=512,
                    temperature=0.3
                )
                llm_output = response.choices[0].message.content.strip()

                # Track LLM call for token usage
                asyncio.create_task(track_llm_call(self, "gpt-4o-mini", prompt, response, "dialog_analysis"))

                import json
                try:
                    llm_json = json.loads(llm_output)
                    summary = llm_json.get("summary")
                    learnings = llm_json.get("learnings")
                    score = llm_json.get("score")
                    justification = llm_json.get("justification")
                except Exception as e:
                    summary = learnings = score = justification = None
                    self.logger.error(f"Failed to parse LLM output as JSON: {e}. Output: {llm_output}")
            except Exception as e:
                llm_output = f"[LLM summary failed: {e}]"
                summary = learnings = score = justification = None

            # Write to file
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(f"Session ID: {session_id}\n")
                f.write(f"User ID: {self.user_id}\n")
                f.write(f"Aggregated Latency Sums: {latency_sums}\n")
                f.write(f"Duration: {total_session_latency} ms\n")
                f.write(f"Total Input Tokens: {total_tokens_in}\n")
                f.write(f"Total Output Tokens: {total_tokens_out}\n")
                f.write(f"Total Estimated Cost: ${total_estimated_cost_usd:.6f} USD\n")
                f.write(f"TTS Characters Used: {tts_characters_used}\n")
                f.write("\n=== Token Usage Details ===\n")
                # Group calls by turn_id
                calls_by_turn = defaultdict(list)
                for detail in token_details:
                    calls_by_turn[detail.get("turn_id", 0)].append(detail)
                for turn_id in sorted(calls_by_turn.keys()):
                    f.write(f"Turn {turn_id}:\n")
                    turn_total_cost = 0.0
                    for detail in calls_by_turn[turn_id]:
                        model_type = detail.get("context", "text")
                        turn_total_cost += detail.get("cost_usd", 0.0)
                        if model_type == "stt":
                            f.write(f"  STT: {detail['model']} - Audio duration: {detail.get('audio_duration_sec', '?'):.2f}s, Cost: ${detail['cost_usd']:.6f}\n")
                        elif model_type == "tts":
                            f.write(f"  TTS: {detail['model']} - Character count: {detail.get('character_count', '?')}, Cost: ${detail['cost_usd']:.6f}\n")
                        else:
                            context_label = detail.get('context', 'LLM')
                            f.write(f"  {context_label}: {detail['model']} - In: {detail['input_tokens']}, Out: {detail['output_tokens']}, Cost: ${detail['cost_usd']:.6f}\n")
                    f.write(f"  Total turn cost: ${turn_total_cost:.6f}\n\n")
                f.write("\n=== LLM Session Summary ===\n")
                # Print summary, learnings, score, justification
                if summary:
                    f.write(f"Summary: {summary}\n")
                if learnings:
                    f.write("Learnings:\n")
                    if isinstance(learnings, list):
                        for line in learnings:
                            f.write(f"- {line}\n")
                    else:
                        f.write(str(learnings) + "\n")
                if score:
                    f.write(f"Score: {score}\n")
                if justification:
                    f.write(f"Justification: {justification}\n")
                f.write("\n=== Conversation (Readable) ===\n")
                # Write readable conversation (user and AI turns)
                for turn in conversation:
                    ts = turn.get("timestamp", "?")
                    role = turn.get("role", "?")
                    text = turn.get("text", "")
                    intent = turn.get("intent", context.get("intent", "unknown"))
                    emotion = context.get("emotion", "unknown")
                    latency = {k: v for k, v in turn.items() if k.startswith("latency")}
                    total_latency = turn.get("total_latency")
                    f.write(f"[{ts}] {role.upper()}: {text}\n  intent: {intent} | emotion: {emotion} | latency: {latency} | total_latency: {total_latency}\n")
            self.logger.info(
                "Saved dialog log to file",
                action="save_dialog_log",
                input_data={"file_path": str(file_path)},
                layer="memory_manager"
            )

            # Save to MongoDB
            try:
                await save_dialog_to_mongo(
                    session_id=session_id,
                    user_id=self.user_id,
                    duration=total_session_latency,
                    score=int(score) if score and str(score).isdigit() else None,
                    agent_performance=justification or ""
                )
                self.logger.info(
                    "Saved dialog log to MongoDB",
                    action="save_dialog_log_mongo",
                    input_data={"session_id": session_id, "user_id": self.user_id},
                    layer="memory_manager"
                )
            except Exception as e:
                self.logger.error(
                    f"Failed to save dialog log to MongoDB: {e}",
                    action="save_dialog_log_mongo",
                    input_data={"session_id": self.session_id},
                    reason=str(e),
                    layer="memory_manager"
                )

            # Return ALL metrics needed for call session data
            return {
                "call_score": int(score) if score and str(score).isdigit() else None,
                "total_session_latency": total_session_latency,
                "latency_avg_ms": latency_avg_ms,
                "tts_characters_used": tts_characters_used,
                "duration_sec": total_session_latency / 1000 if total_session_latency else None,
                "latency_sums": latency_sums,
                # New token tracking metrics
                "tokens_in": total_tokens_in,
                "tokens_out": total_tokens_out,
                "estimated_cost_usd": total_estimated_cost_usd,
                "estimated_cost_usd_for_llm": estimated_cost_usd_for_llm,
                "estimated_cost_usd_for_tts": estimated_cost_usd_for_tts,
                "estimated_cost_usd_for_stt": estimated_cost_usd_for_stt,
                "token_details": token_details
            }

        except Exception as e:
            self.logger.error(
                f"Failed to save dialog log: {e}",
                action="save_dialog_log",
                input_data={"session_id": self.session_id},
                reason=str(e),
                layer="memory_manager"
            )
            # Return empty metrics on error
            return {
                "call_score": None,
                "total_session_latency": 0,
                "latency_avg_ms": None,
                "tts_characters_used": 0,
                "duration_sec": None,
                "latency_sums": {},
                # Empty token tracking metrics
                "tokens_in": 0,
                "tokens_out": 0,
                "estimated_cost_usd": 0.0,
                "token_details": []
            }

    async def persist_workflow_summary(self, workflow):
        """
        Extracts workflow information, rules, states, allowed actions, and disallowed/prohibited actions
        from the workflow object and saves them to persistent memory and as a JSON file.
        Args:
            workflow: WorkflowConfig or dict representing the workflow
        """
        # Support both pydantic model and dict
        if hasattr(workflow, 'dict'):
            workflow_dict = workflow.dict()
        else:
            workflow_dict = workflow
        workflow_id = workflow_dict.get("id")
        summary = {
            "id": workflow_dict.get("id"),
            "name": workflow_dict.get("name"),
            "version": workflow_dict.get("version"),
            "states": {},
            "rules": [],
            "allowed_actions": list(workflow_dict.get("allowed_actions", [])),
            "prohibited_actions": list(workflow_dict.get("prohibited_actions", [])),
        }
        states = workflow_dict.get("states", {})
        allowed_tools = set()
        for state_id, state in states.items():
            summary["states"][state_id] = {
                "type": state.get("type"),
                "layer2_id": state.get("layer2_id"),
                "allowed_tools": state.get("allowed_tools", []),
                "expected_input": state.get("expected_input", []),
                "expected_output": state.get("expected_output", [])
            }
            allowed_tools.update(state.get("allowed_tools", []))
            for transition in state.get("transitions", []):
                summary["rules"].append({
                    "from": state_id,
                    "condition": transition.get("condition"),
                    "to": transition.get("target")
                })
        summary["allowed_tools"] = list(allowed_tools)
        # Save to persistent memory (under key: workflow_source_of_truth_{workflow_id})
        await self.set("persistent", f"workflow_source_of_truth_{workflow_id}", summary)
        # Save as JSON file
        file_path = f"workflow_source_of_truth_{workflow_id}.json"
        import json
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        self.logger.info(
            "Persisted workflow summary to persistent memory and file",
            action="persist_workflow_source_of_truth",
            input_data={"file_path": file_path, "memory_key": f"workflow_source_of_truth_{workflow_id}"},
            layer="memory_manager"
        )

    async def save_user_profile(self):
        """
        Save user profile to MongoDB by extracting data from memory layers.
        This method automatically gathers user preferences and behavior patterns
        from the current session's contextual memory.
        """
        try:
            # Extract data from contextual memory
            context = await self.contextual.get_all()

            # Extract voice profile with expected structure
            voice_profile = context.get("voice_profile", {})
            if not isinstance(voice_profile, dict):
                voice_profile = {}

            # Ensure voice_profile has the expected structure
            voice_profile_structured = {
                "emotion_pref": voice_profile.get("emotion_pref", context.get("emotion")),
                "gender_pref": voice_profile.get("gender_pref"),
                "voice_id": voice_profile.get("voice_id")
            }

            # Extract other user preferences from contextual memory
            preferred_language = context.get("preferred_language") or context.get("language")
            default_intent = context.get("default_intent") or context.get("intent")
            tags = context.get("tags", [])
            if not isinstance(tags, list):
                tags = []

            # Calculate feedback score average from conversation
            conversation = context.get("conversation", [])
            feedback_scores = []
            for turn in conversation:
                if turn.get("role") == "ai" and "feedback_score" in turn:
                    feedback_scores.append(turn["feedback_score"])

            feedback_score_avg = sum(feedback_scores) / len(feedback_scores) if feedback_scores else None

            # Save to MongoDB (created_at, updated_at, and call_count will be handled by the database)
            result = await save_user_to_mongo(
                user_id=self.user_id,
                tags=tags,
                voice_profile=voice_profile_structured,
                preferred_language=preferred_language,
                default_intent=default_intent,
                feedback_score_avg=feedback_score_avg,
                schema_version="1.0"
            )

            self.logger.info(
                "Saved user profile to MongoDB",
                action="save_user_profile",
                input_data={
                    "user_id": self.user_id,
                    "voice_profile": voice_profile_structured,
                    "preferred_language": preferred_language,
                    "default_intent": default_intent,
                    "tags": tags,
                    "feedback_score_avg": feedback_score_avg
                },
                output_data={"inserted_id": result},
                layer="memory_manager"
            )
            return result
        except Exception as e:
            self.logger.error(
                f"Failed to save user profile: {e}",
                action="save_user_profile",
                input_data={"user_id": self.user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def _save_call_session_internal(self, dialog_result=None):
        """
        Internal method to save call session metadata extracted from memory layers.

        Args:
            dialog_result: Result from _save_dialog_log_internal() containing ALL calculated metrics and LLM score
        """
        try:
            context = await self.contextual.get_all()

            # Use ALL metrics from dialog log result - no more calculations needed!
            if dialog_result:
                call_score = dialog_result.get("call_score")
                latency_avg_ms = dialog_result.get("latency_avg_ms")
                tts_characters_used = dialog_result.get("tts_characters_used", 0)
                dialog_duration_sec = dialog_result.get("duration_sec")
                # Extract token tracking metrics
                tokens_in = dialog_result.get("tokens_in", 0)
                tokens_out = dialog_result.get("tokens_out", 0)
                estimated_cost_usd = dialog_result.get("estimated_cost_usd", 0.0)
                estimated_cost_usd_for_llm = dialog_result.get("estimated_cost_usd_for_llm", 0.0)
                estimated_cost_usd_for_tts = dialog_result.get("estimated_cost_usd_for_tts", 0.0)
                estimated_cost_usd_for_stt = dialog_result.get("estimated_cost_usd_for_stt", 0.0)
            else:
                # Fallback values if dialog result not available
                call_score = None
                latency_avg_ms = None
                tts_characters_used = 0
                dialog_duration_sec = None
                # Fallback token tracking values
                tokens_in = 0
                tokens_out = 0
                estimated_cost_usd = 0.0
                estimated_cost_usd_for_llm = 0.0
                estimated_cost_usd_for_tts = 0.0
                estimated_cost_usd_for_stt = 0.0

            # Extract session data from contextual memory (now tracked by StateManager)
            agent_profile_used = context.get("agent_profile")
            pipeline_id = context.get("pipeline_id") or context.get("workflow_name")
            states_visited = context.get("states_visited", [])
            interrupts = context.get("interrupts", [])
            outcome = context.get("outcome", "completed")
            final_action = context.get("final_action")

            # Calculate session duration if available
            session_start = context.get("session_start_time")
            session_end = context.get("session_end_time")
            session_duration_sec = None
            if session_start and session_end:
                try:
                    start_dt = datetime.fromisoformat(session_start.replace('Z', '+00:00'))
                    end_dt = datetime.fromisoformat(session_end.replace('Z', '+00:00'))
                    session_duration_sec = (end_dt - start_dt).total_seconds()
                except Exception:
                    pass

            # Use session duration if available, otherwise fall back to dialog-calculated duration
            final_duration_sec = session_duration_sec if session_duration_sec else dialog_duration_sec

            result = await save_call_session_to_mongo(
                session_id=self.session_id,
                user_id=self.user_id,
                timestamp=datetime.now(),
                agent_profile_used=agent_profile_used,
                pipeline_id=pipeline_id,
                states_visited=states_visited,
                interrupts=interrupts,
                outcome=outcome,
                final_action=final_action,
                call_score=call_score,
                duration_sec=final_duration_sec,
                latency_avg_ms=latency_avg_ms,
                tts_characters_used=tts_characters_used,
                tokens_in=tokens_in,
                tokens_out=tokens_out,
                estimated_cost_usd=estimated_cost_usd,
                estimated_cost_usd_for_llm=estimated_cost_usd_for_llm,
                estimated_cost_usd_for_tts=estimated_cost_usd_for_tts,
                estimated_cost_usd_for_stt=estimated_cost_usd_for_stt,
                schema_version="1.0"
            )
            return result

        except Exception as e:
            self.logger.error(
                f"Failed to save call session internally: {e}",
                action="_save_call_session_internal",
                input_data={"session_id": self.session_id, "user_id": self.user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def _save_pipeline_internal(self):
        """
        Internal method to save pipeline metadata extracted from workflow and contextual memory.
        """
        try:
            context = await self.contextual.get_all()

            # Extract pipeline data from contextual memory (set by StateManager)
            pipeline_id = context.get("pipeline_id")
            workflow_name = context.get("workflow_name")

            # Get complete workflow states from contextual memory (set by StateManager)
            workflow_states = context.get("workflow_states", [])

            # Fallback to states_visited if workflow_states not available
            if not workflow_states:
                workflow_states = context.get("states_visited", [])

            # Extract metadata from contextual memory or use defaults
            metadata = {
                "title": workflow_name or "Unknown Workflow",
                "description": context.get("workflow_description", f"Workflow execution for {workflow_name}")
            }

            # Extract version information
            version = context.get("workflow_version", "1.0")

            # Only save if we have essential data
            if pipeline_id and workflow_states:
                result = await save_pipeline_to_mongo(
                    pipeline_id=pipeline_id,
                    user_id=self.user_id,
                    workflow_states=workflow_states,
                    metadata=metadata,
                    version=version,
                    schema_version="1.0"
                )

                self.logger.info(
                    "Saved pipeline metadata",
                    action="_save_pipeline_internal",
                    input_data={
                        "pipeline_id": pipeline_id,
                        "user_id": self.user_id,
                        "workflow_states_count": len(workflow_states),
                        "metadata": metadata
                    },
                    output_data={"result": result},
                    layer="memory_manager"
                )
                return result
            else:
                self.logger.warning(
                    "Insufficient pipeline data to save",
                    action="_save_pipeline_internal",
                    input_data={
                        "pipeline_id": pipeline_id,
                        "workflow_states": workflow_states,
                        "has_essential_data": bool(pipeline_id and workflow_states)
                    },
                    layer="memory_manager"
                )
                return None

        except Exception as e:
            self.logger.error(
                f"Failed to save pipeline internally: {e}",
                action="_save_pipeline_internal",
                input_data={"user_id": self.user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None

    async def _save_intent_history_internal(self):
        """
        Internal method to save intent detection data for each user input turn in the conversation.

        This method processes each user turn and creates intent history records with:
        - Unique intent_id per turn
        - Intent detection results
        - Success determination based on AI response analysis
        - Rich context snapshot for each interaction

        Returns:
            int: Number of intent records saved, or None on error
        """
        try:
            from datetime import datetime

            context = await self.contextual.get_all()
            conversation = context.get("conversation", [])

            if not conversation:
                self.logger.warning(
                    "No conversation data found for intent history",
                    action="_save_intent_history_internal",
                    input_data={"session_id": self.session_id, "user_id": self.user_id},
                    layer="memory_manager"
                )
                return 0

            saved_count = 0

            # Process each turn in the conversation
            for i, turn in enumerate(conversation):
                # Only process user turns
                if turn.get("role") == "user":
                    # Extract intent detection data from contextual memory
                    intent_detected = context.get("intent") or context.get("detected_intent", "unknown")
                    state_id = context.get("current_state", "unknown")

                    # Determine success: if intent was detected (not "unknown"), then it's a success
                    success = intent_detected != "unknown"

                    # Build prior_intents list from previous user turns
                    prior_intents = []
                    for prev_turn in conversation[:i]:
                        if prev_turn.get("role") == "user":
                            prev_intent = prev_turn.get("intent_detected") or prev_turn.get("intent") or context.get("intent")
                            if prev_intent and prev_intent != "unknown":
                                prior_intents.append(prev_intent)

                    # Build context snapshot
                    context_snapshot = {
                        "turn_index": i,
                        "timestamp": turn.get("timestamp") or datetime.now().isoformat(),
                        "current_state": state_id,
                        "prior_intents": prior_intents
                    }

                    # Add optional context data if available
                    if context.get("emotion"):
                        context_snapshot["emotion"] = context.get("emotion")
  
                    # Save intent history record (intent_id will be auto-generated)
                    result = await save_intent_history_to_mongo(
                        user_id=self.user_id,
                        session_id=self.session_id,
                        raw_input=turn.get("text", ""),
                        intent_detected=intent_detected,
                        state_id=state_id,
                        success=success,
                        context_snapshot=context_snapshot,
                        schema_version="1.0"
                    )

                    if result:
                        saved_count += 1

                    self.logger.debug(
                        "Saved intent history record",
                        action="_save_intent_history_internal",
                        input_data={
                            "turn_index": i,
                            "intent_detected": intent_detected,
                            "success": success,
                            "state_id": state_id
                        },
                        layer="memory_manager"
                    )

            self.logger.info(
                f"Saved {saved_count} intent history records",
                action="_save_intent_history_internal",
                input_data={
                    "session_id": self.session_id,
                    "user_id": self.user_id,
                    "total_conversation_turns": len(conversation),
                    "user_turns_processed": saved_count
                },
                output_data={"saved_count": saved_count},
                layer="memory_manager"
            )

            return saved_count

        except Exception as e:
            self.logger.error(
                f"Failed to save intent history internally: {e}",
                action="_save_intent_history_internal",
                input_data={"session_id": self.session_id, "user_id": self.user_id},
                reason=str(e),
                layer="memory_manager"
            )
            return None



#################### interrupt memory ###################33
    async def set_interrupt_context(self, detected: bool = False, confirmed: bool = False,
                                  user_input_queued: str = None, resume_after_acknowledgment: bool = True,
                                  action_reversible: bool = True, interrupt_timestamp: str = None,
                                  handled: bool = False, **kwargs):
        """Set interrupt context in contextual memory."""
        from datetime import datetime

        interrupt_context = {
            "detected": detected,
            "confirmed": confirmed,
            "user_input_queued": user_input_queued,
            "resume_after_acknowledgment": resume_after_acknowledgment,
            "action_reversible": action_reversible,
            "interrupt_timestamp": interrupt_timestamp or datetime.now().isoformat(),
            "handled": handled,
            "last_updated": datetime.now().isoformat()
        }

        # Add any additional kwargs
        interrupt_context.update(kwargs)

        await self.contextual.set("interrupt_context", interrupt_context)

        self.logger.info(
            "Interrupt context updated",
            action="set_interrupt_context",
            input_data=interrupt_context,
            layer="memory_manager"
        )

    async def get_interrupt_context(self) -> dict:
        """Get current interrupt context from contextual memory."""
        return await self.contextual.get("interrupt_context") or {}

    async def add_interrupt_event(self, event_type: str, details: dict = None):
        """Add an interrupt event to the session's interrupt history."""
        from datetime import datetime

        interrupts = await self.contextual.get("interrupts")
        if not isinstance(interrupts, list):
            interrupts = []

    # --- Interrupt Handling Memory Methods ---
    async def set_tts_playback_state(self, audio_path: str, status: str = "playing",
                                   playback_position: float = 0.0, total_duration: float = None,
                                   message_hash: str = None):
        """Set TTS playback state in contextual memory."""
        from datetime import datetime

        playback_state = {
            "status": status,  # "playing", "paused", "completed", "interrupted"
            "audio_path": audio_path,
            "playback_position": playback_position,
            "total_duration": total_duration,
            "message_hash": message_hash,
            "last_updated": datetime.now().isoformat()
        }

        await self.contextual.set("tts_playback", playback_state)

        self.logger.info(
            "TTS playback state updated",
            action="set_tts_playback_state",
            input_data=playback_state,
            layer="memory_manager"
        )

    async def get_tts_playback_state(self) -> dict:
        """Get current TTS playback state from contextual memory."""
        return await self.contextual.get("tts_playback") or {}

    # --- Context Clearing ---
    async def clear_context(self):
        """
        Clear previous intent context for fresh parsing.

        This satisfies the task description requirements:
        - "invalidate or expire the previous intent"
        - "Allow for new intent parsing"
        - "Context Reset or Versioning"
        """
        # Clear previous intent context
        intent_keys = [
            "intent", "intent_confidence", "user_query", "processed_intent",
            "previous_action", "cached_intent", "intent_history", "clean_text",
            "emotion", "gender", "preprocessing_confidence_value",
            "transcript"
        ]

        cleared_count = 0
        for key in intent_keys:
            try:
                await self.clear("contextual", key)
                cleared_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to clear key {key}: {e}")

        # Mark this as an engagement interaction (for analytics/tracking)
        await self.set("contextual", "is_engagement_question", True)

        # Mark context as reset (for analytics/tracking)
        from datetime import datetime
        await self.set("contextual", "context_reset", True)
        await self.set("contextual", "context_reset_timestamp", datetime.now().isoformat())

        self.logger.info(
            f"Context cleared for fresh intent parsing. Cleared {cleared_count}/{len(intent_keys)} keys",
            action="clear_context",
            input_data={"cleared_keys_count": cleared_count},
            layer="memory_manager"
        )
