from typing import List, Dict, Optional, Union
from pydantic import BaseModel
from asteval import Interpreter

# workflow schema

class Transition(BaseModel):
    condition: str
    target: str

class State(BaseModel):
    id: str
    type: str
    layer2_id: str
    expected_input: List[str]
    expected_output: List[str]
    transitions: Optional[List[Transition]] = []
    allowed_tools: List[str]
    engagement_message: Optional[str] = None

class WorkflowConfig(BaseModel):
    id: str
    name: str
    version: str
    start: str
    allowed_actions:List[str]
    prohibited_actions:List[str]
    engagement_messages: Optional[str] = None
    states: Dict[str, State]

class WorkflowWrapper(BaseModel):
    workflow: WorkflowConfig

# pipeline schema

class Tools(BaseModel):
    memory: Optional[str] = None
    external_tools: str


class PipelineStep(BaseModel):
    step: str
    process: str
    agent: str
    input: Dict[str, str]
    tools: Tools
    output: Dict[str, str]


class OnInterrupt(BaseModel):
    handler: str
    resume_from: str


class OnError(BaseModel):
    retry: int
    fallback_state: str


class Layer2(BaseModel):
    id: str
    version: str
    pipeline: List[PipelineStep]
    onInterrupt: Optional[OnInterrupt] = None
    onError: Optional[OnError] = None

class InterruptConfig(BaseModel):
    reversible: bool
    side_effects: bool = False
    confirmation_required: bool = False
    interrupt_message: Optional[str] = None
    description: Optional[str] = None